package event

import (
	"socks/server/domain/entity"
	urlService "socks/server/domain/service"
	"socks/server/util"
	"sync"
)

var (
	urlProxy *URLProxy
	upOnce   sync.Once
)

type URLProxy struct {
	urlProxyService *urlService.URLProxyService
}

func GetURLProxy(config *util.TunnelConfig) *URLProxy {
	upOnce.Do(func() {
		urlProxy = &URLProxy{
			urlProxyService: urlService.GetUrlProxyService(config),
		}
	})
	return urlProxy
}

func (p *URLProxy) RegisterClient(client *entity.Client) error {
	return p.urlProxyService.RegisterClient(client)
}

func (p *URLProxy) RegisterURLMapping(clientUUID, urlPath, baseURL, serviceName, serviceGroup, apiType string) error {
	return p.urlProxyService.RegisterURLMapping(clientUUID, urlPath, baseURL, serviceName, serviceGroup, apiType)
}

func (p *URLProxy) UnregisterURLMapping(clientUUID, urlPath, baseURL string) error {
	return p.urlProxyService.UnregisterURLMapping(clientUUID, urlPath, baseURL)
}

func (p *URLProxy) GetURLMapping(urlPath string) *entity.URLMapping {
	return p.urlProxyService.GetURLMapping(urlPath)
}

func (p *URLProxy) GetClientURLMappings(clientUUID string) []string {
	return p.urlProxyService.GetClientURLMappings(clientUUID)
}

func (p *URLProxy) UpdateOnlineStatus(clientUUID string, online bool) error {
	return p.urlProxyService.UpdateOnlineStatus(clientUUID, online)
}

func (p *URLProxy) SendProxyRequest(clientUUID string, message *entity.URLProxyMessage) (chan *entity.URLProxyMessage, error) {
	return p.urlProxyService.SendProxyRequest(clientUUID, message)
}

func (p *URLProxy) DeleteRespChan(clientUUID, id string) error {
	return p.urlProxyService.DeleteRespChan(clientUUID, id)
}

func (p *URLProxy) GetTunnel(clientUUID string) *entity.SafeConn {
	return p.urlProxyService.GetTunnel(clientUUID)
}
