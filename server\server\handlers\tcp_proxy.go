package handlers

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"socks/server/application/service"
	"socks/server/domain/entity"
	"socks/server/util"
	"strings"
	"sync"
	"time"
)

var (
	tcpProxyHandler *TCPProxyHandler
	tphOnce         sync.Once
)

type TCPProxyHandler struct {
	urlProxyService service.UrlProxyService
}

func GetTCPProxyHandler() *TCPProxyHandler {
	tphOnce.Do(func() {
		tcpProxyHandler = &TCPProxyHandler{
			urlProxyService: service.GetUrlProxyService(util.SystemConfig),
		}
	})
	return tcpProxyHandler
}

// StartTCPProxyServer 启动TCP代理服务器
func (h *TCPProxyHandler) StartTCPProxyServer(port int) error {
	listener, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", port))
	if err != nil {
		return fmt.Errorf("failed to start TCP proxy server on port %d: %v", port, err)
	}

	log.Printf("TCP代理服务器启动，监听端口: %d", port)

	go func() {
		defer listener.Close()
		for {
			conn, err := listener.Accept()
			if err != nil {
				log.Printf("TCP代理服务器接受连接失败: %v", err)
				continue
			}

			// 为每个连接启动处理协程
			go h.handleTCPConnection(conn)
		}
	}()

	return nil
}

// handleTCPConnection 处理TCP连接
func (h *TCPProxyHandler) handleTCPConnection(conn net.Conn) {
	defer conn.Close()

	// 设置连接超时
	conn.SetReadDeadline(time.Now().Add(30 * time.Second))

	// 解析HTTP请求
	reqData, err := h.parseHTTPRequest(conn)
	if err != nil {
		log.Printf("解析HTTP请求失败: %v", err)
		h.sendErrorResponse(conn, 400, "Bad Request")
		return
	}

	log.Printf("TCP代理收到请求: %s %s from %s", reqData.Request.Method, reqData.Request.URL.Path, conn.RemoteAddr())

	// 查找映射并构建目标路径
	mapping, targetPath, baseURL, err := h.lookupMappingAndBuildPath(reqData.Request.URL.Path)
	if err != nil {
		log.Printf("查找映射失败: %v", err)
		h.sendErrorResponse(conn, 404, "Not Found")
		return
	}

	// 检查映射是否在线
	if !mapping.Online {
		log.Printf("服务离线: %s", reqData.Request.URL.Path)
		h.sendErrorResponse(conn, 503, "Service Unavailable")
		return
	}

	// 添加查询参数
	if reqData.Request.URL.RawQuery != "" {
		targetPath = targetPath + "?" + reqData.Request.URL.RawQuery
	}

	// 获取客户端的SafeConn连接
	tunnel := h.urlProxyService.GetTunnel(mapping.Client.UUID)
	if tunnel == nil {
		log.Printf("客户端隧道未找到: %s", mapping.Client.UUID)
		h.sendErrorResponse(conn, 503, "Service Unavailable")
		return
	}

	// 启动TCP连接转发
	h.startTCPForward(conn, reqData, tunnel, baseURL, targetPath)
}

// HTTPRequestData 存储解析的HTTP请求数据
type HTTPRequestData struct {
	Request  *http.Request
	RawData  []byte
	BodyData []byte
	HasBody  bool
}

// parseHTTPRequest 从TCP连接解析HTTP请求并保存原始数据
func (h *TCPProxyHandler) parseHTTPRequest(conn net.Conn) (*HTTPRequestData, error) {
	reader := bufio.NewReader(conn)
	var rawData []byte

	// 读取请求行
	requestLine, _, err := reader.ReadLine()
	if err != nil {
		return nil, fmt.Errorf("读取请求行失败: %v", err)
	}
	rawData = append(rawData, requestLine...)
	rawData = append(rawData, []byte("\r\n")...)

	// 解析请求行
	parts := strings.Split(string(requestLine), " ")
	if len(parts) != 3 {
		return nil, fmt.Errorf("无效的请求行: %s", string(requestLine))
	}

	method := parts[0]
	urlPath := parts[1]
	protocol := parts[2]

	// 创建HTTP请求对象
	req, err := http.NewRequest(method, urlPath, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Proto = protocol
	req.Header = make(http.Header)

	// 读取请求头
	for {
		line, _, err := reader.ReadLine()
		if err != nil {
			return nil, fmt.Errorf("读取请求头失败: %v", err)
		}

		rawData = append(rawData, line...)
		rawData = append(rawData, []byte("\r\n")...)

		// 空行表示请求头结束
		if len(line) == 0 {
			break
		}

		// 解析请求头
		headerLine := string(line)
		colonIndex := strings.Index(headerLine, ":")
		if colonIndex == -1 {
			continue
		}

		headerName := strings.TrimSpace(headerLine[:colonIndex])
		headerValue := strings.TrimSpace(headerLine[colonIndex+1:])
		req.Header.Add(headerName, headerValue)
	}

	// 读取请求体（如果有）
	var bodyData []byte
	hasBody := false

	if contentLengthStr := req.Header.Get("Content-Length"); contentLengthStr != "" {
		var contentLength int
		fmt.Sscanf(contentLengthStr, "%d", &contentLength)
		if contentLength > 0 {
			bodyData = make([]byte, contentLength)
			_, err := io.ReadFull(reader, bodyData)
			if err != nil {
				return nil, fmt.Errorf("读取请求体失败: %v", err)
			}
			hasBody = true
		}
	} else if req.Header.Get("Transfer-Encoding") == "chunked" {
		// 处理chunked编码
		for {
			line, err := reader.ReadBytes('\n')
			if err != nil {
				break
			}
			bodyData = append(bodyData, line...)

			// 如果是结束块（0\r\n），读取最后的\r\n并结束
			if strings.TrimSpace(string(line)) == "0" {
				finalLine, _ := reader.ReadBytes('\n')
				bodyData = append(bodyData, finalLine...)
				break
			}

			// 读取chunk大小对应的数据
			chunkSizeStr := strings.TrimSpace(string(line))
			var chunkSize int
			fmt.Sscanf(chunkSizeStr, "%x", &chunkSize)
			if chunkSize > 0 {
				chunkData := make([]byte, chunkSize+2) // +2 for \r\n
				_, err := io.ReadFull(reader, chunkData)
				if err != nil {
					break
				}
				bodyData = append(bodyData, chunkData...)
			}
		}
		hasBody = true
	}

	return &HTTPRequestData{
		Request:  req,
		RawData:  rawData,
		BodyData: bodyData,
		HasBody:  hasBody,
	}, nil
}

// sendErrorResponse 发送错误响应
func (h *TCPProxyHandler) sendErrorResponse(conn net.Conn, statusCode int, statusText string) {
	response := fmt.Sprintf("HTTP/1.1 %d %s\r\nContent-Type: text/plain\r\nContent-Length: %d\r\n\r\n%s",
		statusCode, statusText, len(statusText), statusText)
	conn.Write([]byte(response))
}

// lookupMappingAndBuildPath 查找映射并构建目标路径（复用URLProxyHandler的逻辑）
func (h *TCPProxyHandler) lookupMappingAndBuildPath(urlPath string) (*entity.URLMapping, string, string, error) {
	urlParts := strings.Split(strings.Trim(urlPath, "/"), "/")
	if len(urlParts) < 4 {
		return nil, "", "", fmt.Errorf("invalid URL path")
	}

	apiType := strings.ToLower(urlParts[1])
	var mapping *entity.URLMapping
	var targetPath string

	// 根据客户端类型查找匹配的映射
	switch apiType {
	case "agent":
		if len(urlParts) >= 4 {
			// Agent路径格式: /Agent/客户端名称/服务名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 4 {
				targetPath = "/" + strings.Join(urlParts[4:], "/")
			} else {
				targetPath = "/"
			}
		}
	case "ai", "api":
		if len(urlParts) >= 5 {
			// AI/API路径格式: /(AI|API)/服务组别/服务名称/客户端名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3], urlParts[4])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 5 {
				targetPath = "/" + strings.Join(urlParts[5:], "/")
			} else {
				targetPath = "/"
			}
		}
	default:
		return nil, "", "", fmt.Errorf("unsupported API type: %s", apiType)
	}

	if mapping == nil {
		return nil, "", "", fmt.Errorf("no mapping found for path: %s", urlPath)
	}

	// 获取baseURL（取第一个）
	var baseURL string
	for url := range mapping.BaseURL {
		baseURL = url
		break
	}

	return mapping, targetPath, baseURL, nil
}

// startTCPForward 启动TCP连接转发
func (h *TCPProxyHandler) startTCPForward(conn net.Conn, reqData *HTTPRequestData, tunnel *entity.SafeConn, baseURL, targetPath string) {
	// 生成唯一的连接ID
	connID := fmt.Sprintf("tcp_%d_%s", time.Now().UnixNano(), conn.RemoteAddr().String())

	defer func() {
		log.Printf("TCP代理连接结束: %s", connID)
		conn.Close()
		closeMsg := entity.ConnMessage{
			ID:   connID,
			Type: "close",
		}

		if err := tunnel.WriteJSON(closeMsg); err != nil {
			log.Printf("发送关闭消息失败: %v", err)
		}
	}()

	log.Printf("开始TCP代理转发: %s", connID)

	// 创建响应通道
	respChan := make(chan []byte, 100)
	tunnel.AddRespChan(connID, respChan)
	defer func() {
		// 安全地关闭通道
		if ch := tunnel.GetResponseChan(connID); ch != nil {
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("通道已经关闭: %s", connID)
					}
				}()
				close(ch)
			}()
		}
		tunnel.DeleteRespChan(connID)
	}()

	// 发送打开连接消息给客户端
	openMsg := entity.ConnMessage{
		ID:   connID,
		Type: "url_open",
		Data: []byte(fmt.Sprintf("%s|%s", baseURL, targetPath)),
	}

	if err := tunnel.WriteJSON(openMsg); err != nil {
		log.Printf("发送打开消息失败: %v", err)
		h.sendErrorResponse(conn, 500, "Internal Server Error")
		return
	}

	// 构建完整的HTTP请求数据
	requestData := h.buildHTTPRequestData(reqData, targetPath)

	// 发送请求数据
	dataMsg := entity.ConnMessage{
		ID:   connID,
		Type: "data",
		Data: requestData,
	}

	if err := tunnel.WriteJSON(dataMsg); err != nil {
		log.Printf("发送请求数据失败: %v", err)
		h.sendErrorResponse(conn, 500, "Internal Server Error")
		return
	}

	// 启动数据转发协程：将respChan的数据直接写入TCP连接
	log.Printf("开始转发响应数据到TCP连接: %s", connID)
	for data := range respChan {
		if len(data) > 0 {
			// 直接将响应数据写入TCP连接
			if _, err := conn.Write(data); err != nil {
				log.Printf("写入TCP连接失败: %s, %v", connID, err)
				break
			}
		}
	}
}

// buildHTTPRequestData 构建完整的HTTP请求数据
func (h *TCPProxyHandler) buildHTTPRequestData(reqData *HTTPRequestData, targetPath string) []byte {
	var requestData []byte

	// 构建请求行
	requestData = append(requestData, []byte(fmt.Sprintf("%s %s %s\r\n", reqData.Request.Method, targetPath, reqData.Request.Proto))...)

	// 添加所有请求头
	for name, values := range reqData.Request.Header {
		for _, v := range values {
			requestData = append(requestData, []byte(fmt.Sprintf("%s: %s\r\n", name, v))...)
		}
	}

	// 添加空行分隔头部和正文
	requestData = append(requestData, []byte("\r\n")...)

	// 添加请求体（如果有）
	if reqData.HasBody {
		requestData = append(requestData, reqData.BodyData...)
	}

	return requestData
}
