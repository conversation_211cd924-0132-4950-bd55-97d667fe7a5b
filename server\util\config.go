package util

import (
	"encoding/json"
	"log"
	"os"
)

var (
	defaultConfigPath = "E:\\BaseOS\\NetWork\\Gateway\\Configs\\Gateway.json"
	SystemConfig      *TunnelConfig
)

// TunnelConfig 存储隧道相关配置
type TunnelConfig struct {
	ManagerPort       int    `json:"IntranetTunnelManagerPort"`
	MinPort           int    `json:"IntranetTunnelMinPort"`
	MaxPort           int    `json:"IntranetTunnelMaxPort"`
	Timeout           int    `json:"IntranetTunnelTimeout"`
	MaxConnection     int    `json:"IntranetTunnelMaxConnection"`
	SlidingExpiration int    `json:"IntranetTunnelSlidingExpiration"`
	DbType            string `json:"GatewayDBType"`
	DbConnectCommand  string `json:"GatewayConnection"`
}

// LoadTunnelConfig 从指定路径加载隧道配置
func LoadTunnelConfig(path string) (*TunnelConfig, error) {
	// 读取文件内容
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	// 解析JSON
	config := &TunnelConfig{}

	if err := json.Unmarshal(data, config); err != nil {
		return nil, err
	}

	SystemConfig = config
	return config, nil
}

func GetSystemConfig(configPath string) *TunnelConfig {
	if SystemConfig != nil {
		return SystemConfig
	}
	if configPath == "" {
		configPath = defaultConfigPath
	}
	config, err := LoadTunnelConfig(configPath)
	if err != nil {
		log.Println(err)
	}

	return config
}
