package dao

import (
	"fmt"
	"sync"
	"time"

	repo "socks/server/infra/repo"
	util "socks/server/util"

	"gorm.io/gorm"
)

var (
	dao  *IntranetTunnelDaoImpl
	once sync.Once
)

type IntranetTunnelDaoImpl struct {
	db gorm.DB
}

func GetIntranetTunnelDaoImpl() *IntranetTunnelDaoImpl {
	once.Do(func() {
		dao = &IntranetTunnelDaoImpl{
			db: *util.GetDB(),
		}
		dao.db.AutoMigrate(&repo.IntranetTunnel{})
	})

	return dao
}

// Create 创建新的隧道记录
func (it *IntranetTunnelDaoImpl) Create(tunnel *repo.IntranetTunnel) (int, error) {
	var existingTunnel repo.IntranetTunnel
	err := it.db.Where("name = ?", tunnel.Name).First(&existingTunnel).Error
	if err == nil {
		tunnel.ID = existingTunnel.ID
	}
	err = it.db.Save(tunnel).Error
	return tunnel.ID, err
}

// GetByID 根据ID获取隧道
func (it *IntranetTunnelDaoImpl) GetIntranetTunnelByID(id int) (*repo.IntranetTunnel, error) {
	var tunnel repo.IntranetTunnel
	err := it.db.First(&tunnel, id).Error
	if err != nil {
		return nil, err
	}
	return &tunnel, nil
}

func (it *IntranetTunnelDaoImpl) GetIntranetTunnelsByIDs(ids []int) ([]*repo.IntranetTunnel, error) {
	tunnels := make([]*repo.IntranetTunnel, 0)
	if len(ids) == 0 {
		return tunnels, nil
	}
	err := it.db.Where("id in (?)", ids).Find(&tunnels).Error
	return tunnels, err
}

// GetAll 获取所有隧道
func (it *IntranetTunnelDaoImpl) GetAll() ([]*repo.IntranetTunnel, error) {
	var tunnels []*repo.IntranetTunnel
	err := it.db.Find(&tunnels).Error
	return tunnels, err
}

// GetEnabled 获取所有启用的隧道
func (it *IntranetTunnelDaoImpl) GetEnabled() ([]*repo.IntranetTunnel, error) {
	var tunnels []*repo.IntranetTunnel
	err := it.db.Where("enable = ?", true).Find(&tunnels).Error
	return tunnels, err
}

// Update 更新隧道信息
func (it *IntranetTunnelDaoImpl) Update(tunnel *repo.IntranetTunnel) error {
	return it.db.Save(tunnel).Error
}

// Delete 删除隧道
func (it *IntranetTunnelDaoImpl) Delete(id int) error {
	return it.db.Delete(&repo.IntranetTunnel{}, id).Error
}

// UpdateLastConnectionTime 更新最后连接时间
func (it *IntranetTunnelDaoImpl) UpdateLastConnectionTime(id int) error {
	sql := fmt.Sprintf("update %s set lastconnectiontime = ? where id = ?", repo.IntranetTunnelTableName)
	return it.db.Exec(sql, time.Now(), id).Error
}

func (it *IntranetTunnelDaoImpl) UpdateOnlineStatus(id int, online bool) error {
	sql := fmt.Sprintf("update %s set online = ? where id = ?", repo.IntranetTunnelTableName)
	return it.db.Exec(sql, online, id).Error
}

// GetExpiredTunnels 获取过期的隧道记录
func (it *IntranetTunnelDaoImpl) GetExpiredTunnels(expiration time.Duration) ([]*repo.IntranetTunnel, error) {
	var tunnels []*repo.IntranetTunnel
	expiredTime := time.Now().Add(-expiration)

	// 查询未启用或不在线且最后连接时间超过过期时间的记录
	err := it.db.Where("(enable = ? OR online = ?) AND lastconnectiontime < ?",
		false, false, expiredTime).Find(&tunnels).Error

	return tunnels, err
}

// BatchDelete 批量删除隧道记录
func (it *IntranetTunnelDaoImpl) BatchDelete(ids []int) error {
	if len(ids) == 0 {
		return nil
	}
	return it.db.Where("id IN (?)", ids).Delete(&repo.IntranetTunnel{}).Error
}
