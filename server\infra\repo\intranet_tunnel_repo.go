package repo

import "time"

const (
	IntranetTunnelTableName = "gateway_intranet_tunnel"
)

type IntranetTunnel struct {
	ID          int       `gorm:"primaryKey;autoIncrement:true;column:id"`
	Name        string    `gorm:"size:100;not null;column:name"`
	ClientUUID  string    `gorm:"size:100;column:clientid"`
	ClientName  string    `gorm:"size:100;column:clientname"`
	ClientIp    string    `gorm:"size:100;column:clientip"`
	Protocol    string    `gorm:"size:20;column:protocol"`
	ServerPort  int       `gorm:"column:serverport"`
	ClientPort  int       `gorm:"column:clientport"`
	Enable      bool      `gorm:"column:enable"`
	Description string    `gorm:"size:500;column:description"`
	Encryption  bool      `gorm:"column:encryption"`
	Password    string    `gorm:"size:100;column:password"`
	RateLimit   int       `gorm:"column:ratelimit"`
	CreateTime  time.Time `gorm:"column:createtime"`
	Connected   time.Time `gorm:"column:lastconnectiontime"`
	Online      bool      `gorm:"column:online"`
	ClientType  string    `gorm:"size:50;column:clienttype"`
	ServiceName string    `gorm:"size:100;column:servicename"`
	ClientRoute string    `gorm:"size:200;column:clientroute"` // 客户端的路由信息，用于URL代理
	ServerRoute string    `gorm:"size:200;column:serverroute"` // 服务端的路由信息，用于URL代理
}

// TableName 指定表名
func (IntranetTunnel) TableName() string {
	return IntranetTunnelTableName
}
